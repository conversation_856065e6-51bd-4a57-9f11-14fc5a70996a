use std::net::SocketAddr;
use std::time::Duration;
use tokio::time::sleep;

// Simple test to verify TCP connection state management
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Testing TCP Connection State Management");
    println!("==========================================");

    // Import the TCP manager from our codebase
    let tcp_manager = tun_proxy_ui::tcp_manager::TcpConnectionManager::new();

    // Test 1: Create a connection to a local service
    println!("\n📡 Test 1: Creating TCP connection...");
    
    let local_addr: SocketAddr = "127.0.0.1:12345".parse()?;
    let remote_addr: SocketAddr = "127.0.0.1:80".parse()?; // HTTP port
    
    match tcp_manager.get_or_create_connection(local_addr, remote_addr).await {
        Ok(connection_id) => {
            println!("✅ Connection created with ID: {}", connection_id);
            
            // Test 2: Check connection state
            println!("\n🔍 Test 2: Checking connection state...");
            
            // Wait a moment for connection to establish
            sleep(Duration::from_millis(100)).await;
            
            if let Some(conn_info) = tcp_manager.get_connection_info(&connection_id).await {
                println!("📊 Connection state: {:?}", conn_info.state);
                println!("📊 Local address: {}", conn_info.local_addr);
                println!("📊 Remote address: {}", conn_info.remote_addr);
                println!("📊 Created at: {:?}", conn_info.created_at);
                
                match conn_info.state {
                    tun_proxy_ui::tcp_manager::TcpConnectionState::Connecting => {
                        println!("⏳ Connection is in Connecting state");
                    }
                    tun_proxy_ui::tcp_manager::TcpConnectionState::Established => {
                        println!("✅ Connection is Established!");
                    }
                    tun_proxy_ui::tcp_manager::TcpConnectionState::Closed => {
                        println!("❌ Connection is Closed");
                    }
                    tun_proxy_ui::tcp_manager::TcpConnectionState::Closing => {
                        println!("🔄 Connection is Closing");
                    }
                }
            } else {
                println!("❌ Could not retrieve connection info");
            }
            
            // Test 3: Send some test data
            println!("\n📤 Test 3: Sending test data...");
            let test_data = b"GET / HTTP/1.1\r\nHost: localhost\r\n\r\n".to_vec();
            
            match tcp_manager.send_data(&connection_id, test_data).await {
                Ok(()) => {
                    println!("✅ Test data sent successfully");
                }
                Err(e) => {
                    println!("❌ Failed to send test data: {}", e);
                }
            }
            
            // Test 4: Try to receive data
            println!("\n📥 Test 4: Attempting to receive data...");
            
            // Wait a bit for potential response
            sleep(Duration::from_millis(500)).await;
            
            match tcp_manager.receive_data(&connection_id).await {
                Ok(Some(data)) => {
                    println!("✅ Received {} bytes of data", data.len());
                    if data.len() > 0 {
                        let preview = String::from_utf8_lossy(&data[..std::cmp::min(100, data.len())]);
                        println!("📄 Data preview: {}", preview);
                    }
                }
                Ok(None) => {
                    println!("ℹ️  No data available");
                }
                Err(e) => {
                    println!("❌ Failed to receive data: {}", e);
                }
            }
            
            // Test 5: Close the connection
            println!("\n🔚 Test 5: Closing connection...");
            
            match tcp_manager.close_connection(&connection_id).await {
                Ok(()) => {
                    println!("✅ Connection closed successfully");
                }
                Err(e) => {
                    println!("❌ Failed to close connection: {}", e);
                }
            }
            
            // Verify connection is closed
            sleep(Duration::from_millis(100)).await;
            
            if let Some(conn_info) = tcp_manager.get_connection_info(&connection_id).await {
                println!("📊 Final connection state: {:?}", conn_info.state);
            }
        }
        Err(e) => {
            println!("❌ Failed to create connection: {}", e);
        }
    }
    
    println!("\n🎉 TCP Connection State Test Complete!");
    Ok(())
}
