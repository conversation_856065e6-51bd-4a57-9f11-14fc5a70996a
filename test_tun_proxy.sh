#!/bin/bash

# TUN代理TCP双向流测试脚本
# 专门测试TUN代理的TCP双向流功能

set -e

echo "🔧 TUN代理TCP双向流测试"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
TUN_PROXY_DIR="./src-tauri"
TUN_PROXY_BINARY="$TUN_PROXY_DIR/target/debug/tun-proxy-ui"
SOCKS5_PROXY_PORT=1080
TEST_SERVER_PORT=8080
TUN_INTERFACE="tun-proxy"

# 检查权限
check_privileges() {
    echo -e "${BLUE}检查管理员权限...${NC}"
    
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}✗${NC} 需要管理员权限来创建TUN接口"
        echo "请使用 sudo 运行此脚本"
        exit 1
    fi
    
    echo -e "${GREEN}✓${NC} 管理员权限确认"
}

# 构建TUN代理
build_tun_proxy() {
    echo -e "${BLUE}构建TUN代理...${NC}"
    
    cd "$TUN_PROXY_DIR"
    
    if cargo build; then
        echo -e "${GREEN}✓${NC} TUN代理构建成功"
    else
        echo -e "${RED}✗${NC} TUN代理构建失败"
        exit 1
    fi
    
    cd - > /dev/null
}

# 启动SOCKS5代理服务器（用于测试）
start_socks5_proxy() {
    echo -e "${BLUE}启动SOCKS5代理服务器...${NC}"
    
    # 使用Python创建一个简单的SOCKS5代理
    cat > /tmp/simple_socks5.py << 'EOF'
#!/usr/bin/env python3
import socket
import threading
import struct

def handle_client(client_socket):
    try:
        # SOCKS5握手
        data = client_socket.recv(1024)
        if len(data) < 3 or data[0] != 5:
            client_socket.close()
            return
        
        # 发送握手响应（无认证）
        client_socket.send(b'\x05\x00')
        
        # 接收连接请求
        data = client_socket.recv(1024)
        if len(data) < 10 or data[0] != 5 or data[1] != 1:
            client_socket.close()
            return
        
        # 解析目标地址
        addr_type = data[3]
        if addr_type == 1:  # IPv4
            addr = socket.inet_ntoa(data[4:8])
            port = struct.unpack('>H', data[8:10])[0]
        else:
            client_socket.send(b'\x05\x08\x00\x01\x00\x00\x00\x00\x00\x00')
            client_socket.close()
            return
        
        # 连接到目标服务器
        try:
            target_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            target_socket.connect((addr, port))
            
            # 发送成功响应
            response = b'\x05\x00\x00\x01' + socket.inet_aton('127.0.0.1') + struct.pack('>H', port)
            client_socket.send(response)
            
            # 开始双向转发
            def forward(src, dst):
                try:
                    while True:
                        data = src.recv(4096)
                        if not data:
                            break
                        dst.send(data)
                except:
                    pass
                finally:
                    src.close()
                    dst.close()
            
            # 启动双向转发线程
            t1 = threading.Thread(target=forward, args=(client_socket, target_socket))
            t2 = threading.Thread(target=forward, args=(target_socket, client_socket))
            t1.start()
            t2.start()
            t1.join()
            t2.join()
            
        except Exception as e:
            # 发送连接失败响应
            client_socket.send(b'\x05\x05\x00\x01\x00\x00\x00\x00\x00\x00')
            client_socket.close()
            
    except Exception as e:
        client_socket.close()

def main():
    server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server.bind(('127.0.0.1', 1080))
    server.listen(5)
    print("SOCKS5 proxy listening on 127.0.0.1:1080")
    
    try:
        while True:
            client, addr = server.accept()
            thread = threading.Thread(target=handle_client, args=(client,))
            thread.start()
    except KeyboardInterrupt:
        server.close()

if __name__ == '__main__':
    main()
EOF

    python3 /tmp/simple_socks5.py &
    SOCKS5_PID=$!
    sleep 2
    
    echo -e "${GREEN}✓${NC} SOCKS5代理服务器已启动 (PID: $SOCKS5_PID)"
}

# 启动测试HTTP服务器
start_test_server() {
    echo -e "${BLUE}启动测试HTTP服务器...${NC}"
    
    # 创建一个简单的测试页面
    mkdir -p /tmp/test_server
    cat > /tmp/test_server/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>TUN Proxy Test Server</title>
</head>
<body>
    <h1>TUN Proxy Test Server</h1>
    <p>If you can see this page, the TUN proxy is working correctly!</p>
    <p>Current time: <span id="time"></span></p>
    <script>
        document.getElementById('time').textContent = new Date().toISOString();
    </script>
</body>
</html>
EOF

    cd /tmp/test_server
    python3 -m http.server $TEST_SERVER_PORT &
    HTTP_SERVER_PID=$!
    cd - > /dev/null
    sleep 2
    
    echo -e "${GREEN}✓${NC} 测试HTTP服务器已启动 (PID: $HTTP_SERVER_PID)"
}

# 启动TUN代理
start_tun_proxy() {
    echo -e "${BLUE}启动TUN代理...${NC}"
    
    # 这里需要根据实际的TUN代理启动方式调整
    # 由于我们的代理是Tauri应用，可能需要不同的启动方式
    
    echo -e "${YELLOW}注意: TUN代理需要手动启动${NC}"
    echo "请在另一个终端中运行:"
    echo "  cd $TUN_PROXY_DIR"
    echo "  sudo cargo run"
    echo ""
    echo "或者启动已构建的二进制文件:"
    echo "  sudo $TUN_PROXY_BINARY"
    echo ""
    echo -e "${YELLOW}按Enter键继续测试（确保TUN代理已启动）...${NC}"
    read -r
}

# 测试直接连接（不通过代理）
test_direct_connection() {
    echo -e "\n${BLUE}测试1: 直接连接${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}测试直接HTTP连接...${NC}"
    if curl -s --connect-timeout 5 http://127.0.0.1:$TEST_SERVER_PORT/ | grep -q "TUN Proxy Test Server"; then
        echo -e "${GREEN}✓${NC} 直接连接成功"
        return 0
    else
        echo -e "${RED}✗${NC} 直接连接失败"
        return 1
    fi
}

# 测试通过SOCKS5代理的连接
test_socks5_connection() {
    echo -e "\n${BLUE}测试2: SOCKS5代理连接${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}测试通过SOCKS5代理的HTTP连接...${NC}"
    if curl -s --connect-timeout 5 --socks5 127.0.0.1:$SOCKS5_PROXY_PORT http://127.0.0.1:$TEST_SERVER_PORT/ | grep -q "TUN Proxy Test Server"; then
        echo -e "${GREEN}✓${NC} SOCKS5代理连接成功"
        return 0
    else
        echo -e "${RED}✗${NC} SOCKS5代理连接失败"
        return 1
    fi
}

# 测试TUN接口
test_tun_interface() {
    echo -e "\n${BLUE}测试3: TUN接口${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}检查TUN接口状态...${NC}"
    if ip link show $TUN_INTERFACE &>/dev/null; then
        echo -e "${GREEN}✓${NC} TUN接口 $TUN_INTERFACE 存在"
        
        # 显示TUN接口信息
        echo "TUN接口信息:"
        ip addr show $TUN_INTERFACE
        
        return 0
    else
        echo -e "${RED}✗${NC} TUN接口 $TUN_INTERFACE 不存在"
        echo "请确保TUN代理正在运行"
        return 1
    fi
}

# 测试TCP双向流
test_tcp_bidirectional() {
    echo -e "\n${BLUE}测试4: TCP双向流${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}测试TCP双向数据传输...${NC}"
    
    # 创建测试数据
    test_data="Hello from TUN proxy test - $(date)"
    
    # 通过netcat测试双向通信
    echo "$test_data" | nc 127.0.0.1 $TEST_SERVER_PORT > /tmp/tun_tcp_result.txt 2>&1 &
    sleep 2
    
    if [ -s /tmp/tun_tcp_result.txt ]; then
        echo -e "${GREEN}✓${NC} TCP双向流测试成功"
        echo "响应数据:"
        head -5 /tmp/tun_tcp_result.txt
    else
        echo -e "${YELLOW}⚠${NC} TCP双向流测试结果不明确"
        echo "这可能是正常的，因为HTTP服务器可能不响应原始TCP数据"
    fi
}

# 测试并发连接
test_concurrent_tcp() {
    echo -e "\n${BLUE}测试5: 并发TCP连接${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}测试并发HTTP请求...${NC}"
    
    # 启动多个并发HTTP请求
    success_count=0
    for i in {1..5}; do
        if curl -s --connect-timeout 3 http://127.0.0.1:$TEST_SERVER_PORT/ | grep -q "TUN Proxy Test Server"; then
            success_count=$((success_count + 1))
        fi &
    done
    
    wait
    
    echo -e "${GREEN}✓${NC} 并发连接成功: $success_count/5"
    
    if [ $success_count -ge 3 ]; then
        echo -e "${GREEN}✓${NC} 并发测试通过"
    else
        echo -e "${YELLOW}⚠${NC} 并发测试部分成功"
    fi
}

# 清理函数
cleanup() {
    echo -e "${YELLOW}清理测试环境...${NC}"
    
    # 停止进程
    [ ! -z "$SOCKS5_PID" ] && kill $SOCKS5_PID 2>/dev/null || true
    [ ! -z "$HTTP_SERVER_PID" ] && kill $HTTP_SERVER_PID 2>/dev/null || true
    
    # 清理临时文件
    rm -f /tmp/simple_socks5.py
    rm -rf /tmp/test_server
    rm -f /tmp/tun_tcp_result.txt
    
    echo -e "${GREEN}✓${NC} 清理完成"
}

# 设置信号处理
trap cleanup EXIT

# 主测试函数
run_tests() {
    echo -e "${BLUE}开始TUN代理TCP双向流测试...${NC}"
    
    check_privileges
    build_tun_proxy
    start_socks5_proxy
    start_test_server
    
    echo -e "\n${GREEN}测试环境准备完成${NC}"
    echo "SOCKS5代理: 127.0.0.1:$SOCKS5_PROXY_PORT"
    echo "测试服务器: 127.0.0.1:$TEST_SERVER_PORT"
    
    start_tun_proxy
    
    # 运行测试
    test_direct_connection
    test_socks5_connection
    test_tun_interface
    test_tcp_bidirectional
    test_concurrent_tcp
    
    echo -e "\n${GREEN}🎉 TUN代理测试完成！${NC}"
    echo ""
    echo "如果所有测试都通过，说明TCP双向流功能正常工作。"
    echo "如果有测试失败，请检查TUN代理的日志输出。"
}

# 显示使用说明
show_usage() {
    echo "用法: sudo $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -c, --clean    仅清理测试环境"
    echo "  -b, --build    仅构建TUN代理"
    echo ""
    echo "注意: 此脚本需要管理员权限来创建TUN接口"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_usage
        exit 0
        ;;
    -c|--clean)
        cleanup
        exit 0
        ;;
    -b|--build)
        build_tun_proxy
        exit 0
        ;;
    "")
        run_tests
        ;;
    *)
        echo -e "${RED}错误: 未知选项 '$1'${NC}"
        show_usage
        exit 1
        ;;
esac
