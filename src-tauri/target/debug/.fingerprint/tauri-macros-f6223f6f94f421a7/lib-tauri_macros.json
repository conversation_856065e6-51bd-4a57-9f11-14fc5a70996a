{"rustc": 12610991425282158916, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 1600788761367804655, "deps": [[2671782512663819132, "tauri_utils", false, 2542594680736475525], [3060637413840920116, "proc_macro2", false, 16533576608068259436], [4974441333307933176, "syn", false, 2247998595589678503], [13077543566650298139, "heck", false, 10538095813257807281], [14455244907590647360, "tauri_codegen", false, 14961869972433077724], [17990358020177143287, "quote", false, 14352002541199012783]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-f6223f6f94f421a7/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}