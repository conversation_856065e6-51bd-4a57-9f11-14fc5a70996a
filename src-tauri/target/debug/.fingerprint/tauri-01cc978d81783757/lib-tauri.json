{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 7605895578623567934, "deps": [[40386456601120721, "percent_encoding", false, 8513023180805923928], [1200537532907108615, "url<PERSON><PERSON>n", false, 13236933696110336055], [1386409696764982933, "objc2", false, 15586145032784159638], [2616743947975331138, "plist", false, 9864099288883896718], [2671782512663819132, "tauri_utils", false, 6997172389806444233], [3150220818285335163, "url", false, 6221166370442567446], [3331586631144870129, "getrandom", false, 9804488184709479465], [3478659405401458569, "tokio", false, 4760918810579231367], [4143744114649553716, "raw_window_handle", false, 13961103894552456421], [4494683389616423722, "muda", false, 3320727502394254255], [4919829919303820331, "serialize_to_javascript", false, 1658186178404321568], [5986029879202738730, "log", false, 11404600273113955178], [6089812615193535349, "tauri_runtime", false, 6948943578716443415], [7573826311589115053, "tauri_macros", false, 2785036659587854860], [8589231650440095114, "embed_plist", false, 1413111778739108935], [9010263965687315507, "http", false, 16117512943758922373], [9689903380558560274, "serde", false, 8729416093781685897], [9859211262912517217, "objc2_foundation", false, 27352014241075893], [10229185211513642314, "mime", false, 16543775317464140076], [10575598148575346675, "objc2_app_kit", false, 10001511970854956021], [10806645703491011684, "thiserror", false, 956444466346166476], [11599800339996261026, "tauri_runtime_wry", false, 12811776730402949224], [11989259058781683633, "dunce", false, 13468389862681908181], [12565293087094287914, "window_vibrancy", false, 11658005902643687737], [12986574360607194341, "serde_repr", false, 13314099403699840003], [13077543566650298139, "heck", false, 10538095813257807281], [13625485746686963219, "anyhow", false, 16973122078729232300], [14039947826026167952, "build_script_build", false, 4254617629265673018], [15367738274754116744, "serde_json", false, 12334649655770589610], [16928111194414003569, "dirs", false, 11575977040727243855], [17155886227862585100, "glob", false, 1180651903813024181]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-01cc978d81783757/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}