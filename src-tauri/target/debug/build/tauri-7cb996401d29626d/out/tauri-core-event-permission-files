["/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-7cb996401d29626d/out/permissions/event/autogenerated/commands/emit.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-7cb996401d29626d/out/permissions/event/autogenerated/commands/emit_to.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-7cb996401d29626d/out/permissions/event/autogenerated/commands/listen.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-7cb996401d29626d/out/permissions/event/autogenerated/commands/unlisten.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-7cb996401d29626d/out/permissions/event/autogenerated/default.toml"]