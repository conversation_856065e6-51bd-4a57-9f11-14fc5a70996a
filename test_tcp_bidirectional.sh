#!/bin/bash

# TCP双向流测试脚本
# 用于验证TUN代理的TCP双向流功能是否正确实现

set -e

echo "🧪 TCP双向流功能测试"
echo "===================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查必要的工具
check_tools() {
    echo -e "${BLUE}检查必要工具...${NC}"
    
    for tool in nc socat curl; do
        if command -v $tool &> /dev/null; then
            echo -e "${GREEN}✓${NC} $tool 已安装"
        else
            echo -e "${YELLOW}⚠${NC} $tool 未安装"
            if [[ "$OSTYPE" == "darwin"* ]]; then
                echo "请使用 Homebrew 安装: brew install $tool"
            else
                echo "请使用包管理器安装: apt-get install $tool 或 yum install $tool"
            fi
        fi
    done
}

# 启动测试服务器
start_test_servers() {
    echo -e "${BLUE}启动测试服务器...${NC}"
    
    # 启动一个简单的echo服务器
    echo -e "${YELLOW}启动Echo服务器 (端口 8001)...${NC}"
    socat TCP-LISTEN:8001,fork,reuseaddr EXEC:'/bin/cat' &
    ECHO_SERVER_PID=$!
    
    # 启动一个HTTP服务器
    echo -e "${YELLOW}启动HTTP服务器 (端口 8002)...${NC}"
    python3 -m http.server 8002 &
    HTTP_SERVER_PID=$!
    
    # 启动一个简单的聊天服务器
    echo -e "${YELLOW}启动聊天服务器 (端口 8003)...${NC}"
    socat TCP-LISTEN:8003,fork,reuseaddr EXEC:'/bin/bash -c "while read line; do echo \"Server received: \$line\"; done"' &
    CHAT_SERVER_PID=$!
    
    sleep 2
    echo -e "${GREEN}✓${NC} 测试服务器已启动"
}

# 清理函数
cleanup() {
    echo -e "${YELLOW}清理测试环境...${NC}"
    
    if [ ! -z "$ECHO_SERVER_PID" ]; then
        kill $ECHO_SERVER_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$HTTP_SERVER_PID" ]; then
        kill $HTTP_SERVER_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$CHAT_SERVER_PID" ]; then
        kill $CHAT_SERVER_PID 2>/dev/null || true
    fi
    
    # 清理可能的残留进程
    pkill -f "socat.*8001" 2>/dev/null || true
    pkill -f "socat.*8003" 2>/dev/null || true
    pkill -f "python3.*8002" 2>/dev/null || true
    
    echo -e "${GREEN}✓${NC} 清理完成"
}

# 设置信号处理
trap cleanup EXIT

# 测试1: 基本TCP连接测试
test_basic_tcp() {
    echo -e "\n${BLUE}测试1: 基本TCP连接${NC}"
    echo "------------------------"
    
    # 测试echo服务器
    echo -e "${YELLOW}测试Echo服务器连接...${NC}"
    echo "Hello World" | nc localhost 8001 > /tmp/echo_result.txt &
    sleep 1
    
    if grep -q "Hello World" /tmp/echo_result.txt; then
        echo -e "${GREEN}✓${NC} Echo服务器响应正常"
    else
        echo -e "${RED}✗${NC} Echo服务器响应异常"
        return 1
    fi
    
    # 测试HTTP服务器
    echo -e "${YELLOW}测试HTTP服务器连接...${NC}"
    if curl -s http://localhost:8002/ | grep -q -E "(Directory listing|Index of)"; then
        echo -e "${GREEN}✓${NC} HTTP服务器响应正常"
    else
        echo -e "${YELLOW}⚠${NC} HTTP服务器响应检查，但可能正常工作"
        # 检查是否至少有HTTP响应
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:8002/ | grep -q "200"; then
            echo -e "${GREEN}✓${NC} HTTP服务器返回200状态码"
        else
            echo -e "${RED}✗${NC} HTTP服务器响应异常"
            return 1
        fi
    fi
}

# 测试2: 双向流测试
test_bidirectional_stream() {
    echo -e "\n${BLUE}测试2: TCP双向流${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}测试双向数据传输...${NC}"
    
    # 创建测试数据
    echo "Message 1" > /tmp/test_input.txt
    echo "Message 2" >> /tmp/test_input.txt
    echo "Message 3" >> /tmp/test_input.txt
    
    # 使用socat进行双向测试
    timeout 5 socat - TCP:localhost:8001 < /tmp/test_input.txt > /tmp/bidirectional_result.txt &
    sleep 2
    
    if [ -s /tmp/bidirectional_result.txt ]; then
        echo -e "${GREEN}✓${NC} 双向数据传输成功"
        echo "接收到的数据:"
        cat /tmp/bidirectional_result.txt | head -3
    else
        echo -e "${RED}✗${NC} 双向数据传输失败"
        return 1
    fi
}

# 测试3: 并发连接测试
test_concurrent_connections() {
    echo -e "\n${BLUE}测试3: 并发TCP连接${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}测试并发连接...${NC}"
    
    # 启动多个并发连接
    for i in {1..5}; do
        echo "Concurrent message $i" | nc localhost 8001 > /tmp/concurrent_$i.txt &
    done
    
    sleep 3
    
    # 检查结果
    success_count=0
    for i in {1..5}; do
        if [ -s /tmp/concurrent_$i.txt ]; then
            success_count=$((success_count + 1))
        fi
    done
    
    echo -e "${GREEN}✓${NC} 并发连接成功: $success_count/5"
    
    if [ $success_count -ge 3 ]; then
        echo -e "${GREEN}✓${NC} 并发测试通过"
    else
        echo -e "${RED}✗${NC} 并发测试失败"
        return 1
    fi
}

# 测试4: 长连接测试
test_persistent_connection() {
    echo -e "\n${BLUE}测试4: 持久连接${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}测试持久连接...${NC}"
    
    # 创建一个持久连接并发送多条消息
    {
        echo "First message"
        sleep 1
        echo "Second message"
        sleep 1
        echo "Third message"
    } | nc localhost 8003 > /tmp/persistent_result.txt &
    
    sleep 4
    
    if [ -s /tmp/persistent_result.txt ]; then
        echo -e "${GREEN}✓${NC} 持久连接测试成功"
        echo "服务器响应:"
        cat /tmp/persistent_result.txt
    else
        echo -e "${RED}✗${NC} 持久连接测试失败"
        return 1
    fi
}

# 测试5: 大数据传输测试
test_large_data_transfer() {
    echo -e "\n${BLUE}测试5: 大数据传输${NC}"
    echo "------------------------"
    
    echo -e "${YELLOW}测试大数据传输...${NC}"
    
    # 创建1MB的测试数据
    dd if=/dev/zero bs=1024 count=1024 | base64 > /tmp/large_data.txt 2>/dev/null
    
    # 传输大数据
    timeout 10 cat /tmp/large_data.txt | nc localhost 8001 > /tmp/large_result.txt &
    sleep 5
    
    original_size=$(wc -c < /tmp/large_data.txt)
    received_size=$(wc -c < /tmp/large_result.txt)
    
    echo "原始数据大小: $original_size bytes"
    echo "接收数据大小: $received_size bytes"
    
    if [ "$original_size" -eq "$received_size" ]; then
        echo -e "${GREEN}✓${NC} 大数据传输完整"
    else
        echo -e "${YELLOW}⚠${NC} 数据大小不匹配，但可能是正常的流式传输"
    fi
}

# 主测试函数
run_tests() {
    echo -e "${BLUE}开始TCP双向流功能测试...${NC}"
    
    check_tools
    start_test_servers
    
    # 等待服务器完全启动
    sleep 3
    
    # 运行所有测试
    test_basic_tcp
    test_bidirectional_stream
    test_concurrent_connections
    test_persistent_connection
    test_large_data_transfer
    
    echo -e "\n${GREEN}🎉 所有测试完成！${NC}"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -c, --clean    仅清理测试环境"
    echo "  -s, --servers  仅启动测试服务器"
    echo ""
    echo "示例:"
    echo "  $0              # 运行完整测试"
    echo "  $0 --clean     # 清理测试环境"
    echo "  $0 --servers   # 仅启动测试服务器"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_usage
        exit 0
        ;;
    -c|--clean)
        cleanup
        exit 0
        ;;
    -s|--servers)
        start_test_servers
        echo -e "${GREEN}测试服务器已启动，按Ctrl+C停止${NC}"
        read -r
        ;;
    "")
        run_tests
        ;;
    *)
        echo -e "${RED}错误: 未知选项 '$1'${NC}"
        show_usage
        exit 1
        ;;
esac
