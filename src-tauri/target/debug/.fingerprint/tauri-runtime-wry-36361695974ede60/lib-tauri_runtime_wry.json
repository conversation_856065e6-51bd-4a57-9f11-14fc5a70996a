{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 18184000869863331234, "deps": [[1386409696764982933, "objc2", false, 8685093890882643508], [2671782512663819132, "tauri_utils", false, 6997172389806444233], [3150220818285335163, "url", false, 6221166370442567446], [4143744114649553716, "raw_window_handle", false, 13961103894552456421], [5986029879202738730, "log", false, 11404600273113955178], [6089812615193535349, "tauri_runtime", false, 6948943578716443415], [8826339825490770380, "tao", false, 4447972798161454781], [9010263965687315507, "http", false, 16117512943758922373], [9141053277961803901, "wry", false, 16865261925329957638], [9859211262912517217, "objc2_foundation", false, 2420309279590556275], [10575598148575346675, "objc2_app_kit", false, 8194001619678705952], [11599800339996261026, "build_script_build", false, 16852782156475176007]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-36361695974ede60/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}