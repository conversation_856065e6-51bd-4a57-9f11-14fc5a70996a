{"rustc": 12610991425282158916, "features": "[\"CFArray\", \"CFCGTypes\", \"CFData\", \"CFDictionary\", \"CFRunLoop\", \"alloc\", \"bitflags\", \"objc2\", \"std\"]", "declared_features": "[\"CFArray\", \"CFAttributedString\", \"CFAvailability\", \"CFBag\", \"CFBase\", \"CFBinaryHeap\", \"CFBitVector\", \"CFBundle\", \"CFByteOrder\", \"CFCGTypes\", \"CFCalendar\", \"CFCharacterSet\", \"CFData\", \"CFDate\", \"CFDateFormatter\", \"CFDictionary\", \"CFError\", \"CFFileDescriptor\", \"CFFileSecurity\", \"CFLocale\", \"CFMachPort\", \"CFMessagePort\", \"CFNotificationCenter\", \"CFNumber\", \"CFNumberFormatter\", \"CFPlugIn\", \"CFPlugInCOM\", \"CFPreferences\", \"CFPropertyList\", \"CFRunLoop\", \"CFSet\", \"CFSocket\", \"CFStream\", \"CFString\", \"CFStringEncodingExt\", \"CFStringTokenizer\", \"CFTimeZone\", \"CFTree\", \"CFURL\", \"CFURLAccess\", \"CFURLEnumerator\", \"CFUUID\", \"CFUserNotification\", \"CFUtilities\", \"CFXMLNode\", \"CFXMLParser\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"dispatch2\", \"libc\", \"objc2\", \"std\", \"unstable-coerce-pointee\"]", "target": 9250166696766853962, "profile": 8196097686603091492, "path": 11104577296263134014, "deps": [[1386409696764982933, "objc2", false, 8685093890882643508], [7896293946984509699, "bitflags", false, 1230295567125418219]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-core-foundation-14854a2cc9d6a1bd/dep-lib-objc2_core_foundation", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}