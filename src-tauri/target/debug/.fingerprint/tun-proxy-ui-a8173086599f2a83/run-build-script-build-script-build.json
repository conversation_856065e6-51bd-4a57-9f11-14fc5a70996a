{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 4254617629265673018], [8324462083842905811, "build_script_build", false, 14943411109295643061], [10075813589556262423, "build_script_build", false, 8206354781236613206]], "local": [{"RerunIfChanged": {"output": "debug/build/tun-proxy-ui-a8173086599f2a83/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}