# TUN代理TCP双向流测试指南

本指南提供了完整的测试流程来验证TUN代理的TCP双向流功能是否正确实现。

## 🎯 测试目标

验证以下关键功能：
- ✅ TCP连接使用`tokio::io::copy_bidirectional`进行真正的双向流转发
- ✅ 持久TCP连接管理（而非请求-响应模式）
- ✅ SOCKS5代理集成
- ✅ TUN接口正确处理TCP流量
- ✅ UI功能正常工作

## 📋 测试脚本说明

### 1. `test_tcp_bidirectional.sh` - 基础TCP双向流测试
测试基本的TCP双向流功能，不依赖TUN代理。

**功能：**
- 启动测试服务器（Echo、HTTP、聊天服务器）
- 测试基本TCP连接
- 验证双向数据传输
- 测试并发连接
- 测试持久连接
- 测试大数据传输

**使用方法：**
```bash
# 运行完整测试
./test_tcp_bidirectional.sh

# 仅启动测试服务器
./test_tcp_bidirectional.sh --servers

# 清理测试环境
./test_tcp_bidirectional.sh --clean
```

### 2. `test_tun_proxy.sh` - TUN代理功能测试
测试TUN代理的TCP双向流功能。

**功能：**
- 构建TUN代理应用
- 启动SOCKS5代理服务器
- 启动测试HTTP服务器
- 测试TUN接口创建
- 验证通过TUN代理的TCP连接
- 测试并发连接

**使用方法：**
```bash
# 运行完整测试（需要sudo权限）
sudo ./test_tun_proxy.sh

# 仅构建TUN代理
./test_tun_proxy.sh --build

# 清理测试环境
sudo ./test_tun_proxy.sh --clean
```

**注意：** 此脚本需要管理员权限来创建TUN接口。

### 3. `test_ui.sh` - UI功能测试
测试Tauri应用的UI功能。

**功能：**
- 检查开发环境（Node.js、Rust、Tauri CLI）
- 安装前端依赖
- 构建前端和后端
- 运行开发模式
- 测试构建的应用

**使用方法：**
```bash
# 运行完整UI测试
./test_ui.sh

# 仅检查环境
./test_ui.sh --check

# 仅构建应用
./test_ui.sh --build

# 运行开发模式
./test_ui.sh --dev

# 测试构建的应用
./test_ui.sh --test
```

## 🚀 推荐测试流程

### 步骤1: 环境准备
```bash
# 检查UI开发环境
./test_ui.sh --check

# 安装必要工具（如果需要）
# Ubuntu/Debian:
sudo apt-get update
sudo apt-get install -y netcat-openbsd socat curl telnet python3

# CentOS/RHEL:
sudo yum install -y nc socat curl telnet python3
```

### 步骤2: 基础功能测试
```bash
# 测试基础TCP双向流功能
./test_tcp_bidirectional.sh
```

### 步骤3: TUN代理测试
```bash
# 构建TUN代理
./test_tun_proxy.sh --build

# 运行TUN代理测试（需要sudo）
sudo ./test_tun_proxy.sh
```

### 步骤4: UI测试
```bash
# 构建UI应用
./test_ui.sh --build

# 测试开发模式
./test_ui.sh --dev

# 或测试构建的应用
./test_ui.sh --test
```

## 🔍 测试验证点

### TCP双向流验证
- [ ] TCP连接能够同时发送和接收数据
- [ ] 连接保持持久状态，不会为每个数据包创建新连接
- [ ] 大数据传输完整性
- [ ] 并发连接处理正确

### TUN代理验证
- [ ] TUN接口成功创建
- [ ] 流量正确路由通过TUN接口
- [ ] SOCKS5代理集成工作正常
- [ ] TCP连接通过代理正确建立

### UI功能验证
- [ ] 应用正常启动
- [ ] 代理配置界面功能正常
- [ ] 连接/断开功能工作
- [ ] 状态显示正确
- [ ] 错误处理适当

## 🐛 故障排除

### 常见问题

**1. TUN接口创建失败**
```bash
# 检查权限
sudo -v

# 检查TUN模块
lsmod | grep tun
sudo modprobe tun
```

**2. SOCKS5连接失败**
```bash
# 检查端口占用
netstat -tlnp | grep 1080

# 测试SOCKS5代理
curl --socks5 127.0.0.1:1080 http://httpbin.org/ip
```

**3. 构建失败**
```bash
# 更新Rust
rustup update

# 清理构建缓存
cd src-tauri
cargo clean
cargo build
```

**4. UI启动失败**
```bash
# 检查Node.js版本
node --version  # 建议 >= 16

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

### 日志查看

**TUN代理日志：**
```bash
# 在开发模式下查看详细日志
cd src-tauri
RUST_LOG=debug cargo tauri dev
```

**系统网络日志：**
```bash
# 查看网络接口
ip addr show

# 监控网络流量
sudo tcpdump -i tun-proxy

# 查看路由表
ip route show
```

## 📊 性能测试

### 吞吐量测试
```bash
# 使用iperf3测试吞吐量
# 服务器端
iperf3 -s -p 5001

# 客户端（通过代理）
iperf3 -c 127.0.0.1 -p 5001 -t 30
```

### 延迟测试
```bash
# 测试延迟
ping -c 10 127.0.0.1

# 通过代理测试延迟
# （需要配置代理后测试）
```

## 📝 测试报告

测试完成后，请记录以下信息：

- [ ] 操作系统版本
- [ ] Rust版本
- [ ] Node.js版本
- [ ] 测试结果（通过/失败）
- [ ] 性能指标
- [ ] 发现的问题
- [ ] 改进建议

## 🔗 相关资源

- [Tokio文档](https://docs.rs/tokio/)
- [Tauri文档](https://tauri.app/)
- [TUN/TAP接口文档](https://www.kernel.org/doc/Documentation/networking/tuntap.txt)
- [SOCKS5协议规范](https://tools.ietf.org/html/rfc1928)

---

**注意：** 在生产环境中部署前，请确保所有测试都通过，并进行充分的安全性评估。
