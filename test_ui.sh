#!/bin/bash

# TUN代理UI测试脚本
# 测试Tauri应用的UI功能

set -e

echo "🖥️  TUN代理UI测试"
echo "================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
TUN_PROXY_DIR="./src-tauri"
FRONTEND_DIR="."

# 检查Node.js和npm
check_frontend_tools() {
    echo -e "${BLUE}检查前端工具...${NC}"
    
    if command -v node &> /dev/null; then
        echo -e "${GREEN}✓${NC} Node.js $(node --version)"
    else
        echo -e "${RED}✗${NC} Node.js 未安装"
        echo "请安装Node.js: https://nodejs.org/"
        exit 1
    fi
    
    if command -v npm &> /dev/null; then
        echo -e "${GREEN}✓${NC} npm $(npm --version)"
    else
        echo -e "${RED}✗${NC} npm 未安装"
        exit 1
    fi
}

# 检查Rust和Cargo
check_rust_tools() {
    echo -e "${BLUE}检查Rust工具...${NC}"
    
    if command -v rustc &> /dev/null; then
        echo -e "${GREEN}✓${NC} Rust $(rustc --version)"
    else
        echo -e "${RED}✗${NC} Rust 未安装"
        echo "请安装Rust: https://rustup.rs/"
        exit 1
    fi
    
    if command -v cargo &> /dev/null; then
        echo -e "${GREEN}✓${NC} Cargo $(cargo --version)"
    else
        echo -e "${RED}✗${NC} Cargo 未安装"
        exit 1
    fi
}

# 检查Tauri CLI
check_tauri_cli() {
    echo -e "${BLUE}检查Tauri CLI...${NC}"
    
    if command -v cargo-tauri &> /dev/null; then
        echo -e "${GREEN}✓${NC} Tauri CLI 已安装"
    else
        echo -e "${YELLOW}⚠${NC} Tauri CLI 未安装，正在安装..."
        cargo install tauri-cli
        echo -e "${GREEN}✓${NC} Tauri CLI 安装完成"
    fi
}

# 安装前端依赖
install_frontend_deps() {
    echo -e "${BLUE}安装前端依赖...${NC}"
    
    if [ -f "package.json" ]; then
        echo -e "${YELLOW}安装npm依赖...${NC}"
        npm install
        echo -e "${GREEN}✓${NC} 前端依赖安装完成"
    else
        echo -e "${YELLOW}⚠${NC} 未找到package.json，跳过前端依赖安装"
    fi
}

# 构建前端
build_frontend() {
    echo -e "${BLUE}构建前端...${NC}"
    
    if [ -f "package.json" ] && npm run build &>/dev/null; then
        echo -e "${GREEN}✓${NC} 前端构建成功"
    else
        echo -e "${YELLOW}⚠${NC} 前端构建跳过或失败"
    fi
}

# 检查Tauri配置
check_tauri_config() {
    echo -e "${BLUE}检查Tauri配置...${NC}"
    
    if [ -f "$TUN_PROXY_DIR/tauri.conf.json" ]; then
        echo -e "${GREEN}✓${NC} Tauri配置文件存在"
        
        # 显示基本配置信息
        echo "应用信息:"
        if command -v jq &> /dev/null; then
            jq '.package.productName, .package.version' "$TUN_PROXY_DIR/tauri.conf.json"
        else
            grep -E '"productName"|"version"' "$TUN_PROXY_DIR/tauri.conf.json" | head -2
        fi
    else
        echo -e "${RED}✗${NC} Tauri配置文件不存在"
        exit 1
    fi
}

# 构建Tauri应用
build_tauri_app() {
    echo -e "${BLUE}构建Tauri应用...${NC}"
    
    cd "$TUN_PROXY_DIR"
    
    echo -e "${YELLOW}开始构建（这可能需要几分钟）...${NC}"
    if cargo tauri build --debug; then
        echo -e "${GREEN}✓${NC} Tauri应用构建成功"
    else
        echo -e "${RED}✗${NC} Tauri应用构建失败"
        cd - > /dev/null
        exit 1
    fi
    
    cd - > /dev/null
}

# 运行开发模式
run_dev_mode() {
    echo -e "${BLUE}启动开发模式...${NC}"
    
    cd "$TUN_PROXY_DIR"
    
    echo -e "${YELLOW}启动Tauri开发服务器...${NC}"
    echo -e "${YELLOW}注意: 这将打开应用窗口，请手动测试UI功能${NC}"
    echo -e "${YELLOW}按Ctrl+C停止开发服务器${NC}"
    echo ""
    
    cargo tauri dev
    
    cd - > /dev/null
}

# 测试构建的应用
test_built_app() {
    echo -e "${BLUE}测试构建的应用...${NC}"
    
    # 查找构建的可执行文件
    app_path=""
    if [ -f "$TUN_PROXY_DIR/target/debug/tun-proxy-ui" ]; then
        app_path="$TUN_PROXY_DIR/target/debug/tun-proxy-ui"
    elif [ -f "$TUN_PROXY_DIR/target/debug/bundle/macos/tun-proxy-ui.app/Contents/MacOS/tun-proxy-ui" ]; then
        app_path="$TUN_PROXY_DIR/target/debug/bundle/macos/tun-proxy-ui.app/Contents/MacOS/tun-proxy-ui"
    elif [ -f "$TUN_PROXY_DIR/target/debug/bundle/appimage/tun-proxy-ui_*_amd64.AppImage" ]; then
        app_path=$(ls "$TUN_PROXY_DIR/target/debug/bundle/appimage/tun-proxy-ui_"*"_amd64.AppImage" | head -1)
    fi
    
    if [ -n "$app_path" ] && [ -f "$app_path" ]; then
        echo -e "${GREEN}✓${NC} 找到构建的应用: $app_path"
        echo -e "${YELLOW}启动应用进行测试...${NC}"
        echo -e "${YELLOW}请手动测试以下功能:${NC}"
        echo "  1. 应用是否正常启动"
        echo "  2. UI界面是否正确显示"
        echo "  3. 代理设置功能"
        echo "  4. 连接/断开功能"
        echo "  5. 状态显示"
        echo ""
        echo -e "${YELLOW}按Enter键启动应用...${NC}"
        read -r
        
        # 启动应用
        "$app_path" &
        APP_PID=$!
        
        echo -e "${GREEN}✓${NC} 应用已启动 (PID: $APP_PID)"
        echo -e "${YELLOW}请在应用中测试功能，完成后按Enter键继续...${NC}"
        read -r
        
        # 停止应用
        kill $APP_PID 2>/dev/null || true
        echo -e "${GREEN}✓${NC} 应用已停止"
    else
        echo -e "${RED}✗${NC} 未找到构建的应用"
        echo "请先运行构建命令"
    fi
}

# 运行基本检查
run_basic_checks() {
    echo -e "${BLUE}运行基本检查...${NC}"
    
    # 检查源代码文件
    echo -e "${YELLOW}检查源代码文件...${NC}"
    
    key_files=(
        "$TUN_PROXY_DIR/src/main.rs"
        "$TUN_PROXY_DIR/src/lib.rs"
        "$TUN_PROXY_DIR/src/tun.rs"
        "$TUN_PROXY_DIR/src/tcp_manager.rs"
        "$TUN_PROXY_DIR/Cargo.toml"
        "$TUN_PROXY_DIR/tauri.conf.json"
    )
    
    for file in "${key_files[@]}"; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}✓${NC} $file"
        else
            echo -e "${RED}✗${NC} $file (缺失)"
        fi
    done
    
    # 检查前端文件
    if [ -f "index.html" ]; then
        echo -e "${GREEN}✓${NC} index.html"
    else
        echo -e "${YELLOW}⚠${NC} index.html (可能使用其他前端框架)"
    fi
}

# 显示测试报告
show_test_report() {
    echo -e "\n${BLUE}测试报告${NC}"
    echo "============"
    echo ""
    echo "✅ 推荐的测试流程:"
    echo "1. 运行基本检查: $0 --check"
    echo "2. 构建应用: $0 --build"
    echo "3. 测试开发模式: $0 --dev"
    echo "4. 测试构建的应用: $0 --test"
    echo ""
    echo "🔧 手动测试项目:"
    echo "- UI界面是否正确显示"
    echo "- 代理服务器配置功能"
    echo "- 连接/断开代理功能"
    echo "- 流量统计显示"
    echo "- 错误处理和用户反馈"
    echo ""
    echo "📝 注意事项:"
    echo "- TUN接口需要管理员权限"
    echo "- 某些功能可能需要实际的网络环境"
    echo "- 建议在虚拟机中进行完整测试"
}

# 主测试函数
run_full_test() {
    echo -e "${BLUE}开始完整UI测试...${NC}"
    
    check_frontend_tools
    check_rust_tools
    check_tauri_cli
    install_frontend_deps
    build_frontend
    check_tauri_config
    run_basic_checks
    
    echo -e "\n${GREEN}✓${NC} 环境检查完成"
    echo ""
    echo -e "${YELLOW}选择测试模式:${NC}"
    echo "1. 构建并测试应用"
    echo "2. 运行开发模式"
    echo "3. 仅显示测试报告"
    echo ""
    read -p "请选择 (1-3): " choice
    
    case $choice in
        1)
            build_tauri_app
            test_built_app
            ;;
        2)
            run_dev_mode
            ;;
        3)
            show_test_report
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            exit 1
            ;;
    esac
    
    echo -e "\n${GREEN}🎉 UI测试完成！${NC}"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -c, --check    仅运行基本检查"
    echo "  -b, --build    仅构建应用"
    echo "  -d, --dev      运行开发模式"
    echo "  -t, --test     测试构建的应用"
    echo "  -r, --report   显示测试报告"
    echo ""
    echo "示例:"
    echo "  $0              # 运行完整测试"
    echo "  $0 --check     # 仅检查环境"
    echo "  $0 --build     # 仅构建应用"
    echo "  $0 --dev       # 运行开发模式"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_usage
        exit 0
        ;;
    -c|--check)
        check_frontend_tools
        check_rust_tools
        check_tauri_cli
        check_tauri_config
        run_basic_checks
        ;;
    -b|--build)
        build_tauri_app
        ;;
    -d|--dev)
        run_dev_mode
        ;;
    -t|--test)
        test_built_app
        ;;
    -r|--report)
        show_test_report
        ;;
    "")
        run_full_test
        ;;
    *)
        echo -e "${RED}错误: 未知选项 '$1'${NC}"
        show_usage
        exit 1
        ;;
esac
