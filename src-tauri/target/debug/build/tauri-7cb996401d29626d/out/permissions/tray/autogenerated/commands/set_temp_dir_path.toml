# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-temp-dir-path"
description = "Enables the set_temp_dir_path command without any pre-configured scope."
commands.allow = ["set_temp_dir_path"]

[[permission]]
identifier = "deny-set-temp-dir-path"
description = "Denies the set_temp_dir_path command without any pre-configured scope."
commands.deny = ["set_temp_dir_path"]
